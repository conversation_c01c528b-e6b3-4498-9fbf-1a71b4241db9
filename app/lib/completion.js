import {
    getAgentByHost,
    getModelProvider,
    getAgentTokenFromHeader,
    getAgentTokenFromQuery,
} from './agent';
import {
    detectLanguage,
    translateToLanguage
} from './language';
import {
    envBoolean,
    replaceTokens,
    shouldIncludeSource,
    agentIsInactive,
    shouldUseRealTimeTranslation,
    getMessageText,
    camelToSnake,
    getMessageCta,
    debug,
} from './helpers';
import {
    getPayloadValidation,
    requestIsSameOrigin
} from './request';
import {
    transformCompletion
} from './format';
import {
    getExtensibleAgentById,
    getAgentModelById,
    getAgentModelByKey,
    getAgentConfig,
    insertAgentConfig,
    insertPrompt,
    getRecentPrompts,
    updatePrompt,
    insertPromptSource,
    getSessionById,
    insertSession,
    getCtas,
    getResponseCounts,
} from './db';
import {
    get503Response,
    get403Response,
    get408Response,
    get422Response
} from './response';
import {
    prependMetaFilterCombinator,
    searchCorpus,
    formatDocumentIds,
    filterUniqueDocs,
} from './vectara';
import { expandRagDocumentAttrs } from './rag';
import { decode } from 'html-entities';
import { getSessionValue } from './session';
import { waitUntil } from '@vercel/functions';
import {
    generateText,
    streamText,
    convertToModelMessages,
    smoothStream,
    createUIMessageStream,
    createUIMessageStreamResponse, simulateReadableStream,
} from 'ai';
import { formatDataStreamPart } from '@ai-sdk/ui-utils';
import { headers } from 'next/headers';
import { decryptBackend } from './crypt';
import { isNull } from 'drizzle-orm';
import * as tables from '../../db/schema';
import { getCachedValue, setCachedValue, getCacheTtl } from './cache';

const API_KEY_HEADER = 'x-api-key';
const COMPLETION_HEADER = 'x-completion';
const API_KEY_PARAM = 'api_key';
const CONTENT_TYPES = {
    text: 'text/plain',
    html: 'text/html',
    json: 'application/json',
    raw: 'text/plain',
};
const EMPTY_RAG_CONTEXT = {sources:[],text:''};
const REASONING_START_SEQUENCE = '<think>';
const REASONING_END_SEQUENCE = '</think>';
const SYSTEM_ROLES = ['system', 'developer'];
const STRIP_MARKDOWN_INSTRUCTIONS = "\n\nDeliver the response in plain text without any Markdown or formatting. Provide the output as raw text.";
const MODEL_PARAM_DEFAULTS = {
    maxTokens: process.env.COMPLETION_API_DEFAULT_MAX_TOKENS,
    temperature: 1,
    topP: 1,
    frequencyPenalty: 0,
    presencePenalty: 0,
};

// Payload schema validator -- had to precompile b/c the compiler won't run on edge runtime
const validatePayload = require('../api/v1/chat/completions/validation');

let shouldLog = false;
let cachedResponse = false;

// Handle the chat completion
export async function handleCompletion(payload, req, integration = null) {

    const startTime = performance.now();

    debug(true, 'REQUEST', payload);

    delete payload.id;
    delete payload.trigger;
    const validation = await getPayloadValidation(payload, validatePayload);
    if (validation) {
        debug(true, 'VALIDATION ERROR', validation);
        return get422Response(validation);
    }

    // Get Agent by hostname; bail if there's no match
    const AGENT = await getAgentByHost(req.headers.get('host'));
    // debug(shouldLog, 'AGENT', AGENT);
    if (
        agentIsInactive(AGENT) ||
        (
            integration &&
            (integration.agent_id !== AGENT.id)
        )
    ) {
        return get503Response();
    }

    // Bail if this is an API request and the API token is invalid
    const sameOrigin = requestIsSameOrigin(req.headers);
    const tokenId = await getAgentTokenFromHeader(AGENT.id, req.headers, API_KEY_HEADER) ||
        await getAgentTokenFromQuery(AGENT.id, req.nextUrl.searchParams, API_KEY_PARAM)
    ;
    if (!sameOrigin && !tokenId) {
        return get403Response();
    }

    const headers = Object.fromEntries(await req.headers.entries());
    const query = Object.fromEntries(req.nextUrl.searchParams.entries());
    shouldLog = (payload.debug || query.debug || headers['x-debug'] || AGENT.debug);
    const cacheTtl = getCacheTtl(headers, query);
    debug(shouldLog, 'REQUEST', { body: payload, headers: headers, query: query });
    debug(shouldLog, 'CACHE TTL', cacheTtl);

    // If the response-format header is present, use that to set payload value
    if (headers['x-response-format']) {
        payload.response_format = { type: headers['x-response-format'] };
    }

    // Set defaults
    const metadataDefaults = {
        anonymous: false,
        conversation: null,
        max_memories: null,
        parent_url: null,
        parent_host: null,
        session: null,
        device: null,
        translation: AGENT.default_translation || process.env.NEXT_PUBLIC_DEFAULT_TRANSLATION,
    };
    if (!payload.metadata) payload.metadata = {};
    for (const param in metadataDefaults) {
        if (!payload.metadata[param]) {
            payload.metadata[param] = metadataDefaults[param];
        }
    }

    // Determine what client is hitting this endpoint
    const client = integration ? 'integration' : await getClient(tokenId, payload.metadata.parent_host);
    const isFrontend = ['standalone', 'embedded'].includes(client);

    // Figure out stream mode
    if (payload.stream === undefined) {
        payload.stream = (!isFrontend && (AGENT.api_response_format === 'raw')) ? false : envBoolean(process.env.NEXT_PUBLIC_STREAM);
        if (payload.stream) {
            payload.stream_options = {
                include_usage: true,
            };
        }
    }

    // Figure out the format
    if (isFrontend) {
        payload.response_format = { type: 'html'};
    } else {
        if (!payload.response_format) {
            payload.response_format = (client === 'api') ? { type: AGENT.api_response_format } : { type: 'text' };
        }
        if (!payload.stream && (payload.response_format.type === 'raw')) {
            payload.response_format.type = 'json';
        }
    }

    debug(shouldLog, 'CLIENT', client);
    debug(shouldLog, 'FORMAT', payload.response_format.type);

    // Determine the model to query
    const MODEL = payload.model ? await getAgentModelByKey(payload.model) : await getAgentModelById(AGENT.model_id);
    if (!MODEL) {
        return get422Response({ success: false, errors: ['Invalid model.'] });
    }
    const PROVIDER = getModelProvider(MODEL);
    debug(shouldLog, 'MODEL', MODEL);

    // Set the session ID if not API
    if (isFrontend && !payload.metadata.session) {
        payload.metadata.session = await getSessionValue('session_id');
    }

    // Log the session if a session ID is set
    if (payload.metadata.session) {
        waitUntil(logSession(payload.metadata.session, payload.metadata.language, payload.metadata.parent_url, payload.metadata.parent_host, payload.user));
    }
    debug(shouldLog, 'SESSION', payload.metadata.session ?? null);

    // Set the prompt and autodetect language if not set
    if (payload.messages && (payload.messages.length > 0) && (payload.messages[0].parts)) {
        payload.messages = convertToModelMessages(payload.messages);
        debug(shouldLog, 'LAST MESSAGE', payload.messages[payload.messages.length-1]);
    }

    const prompt = payload.prompt ?? getMessageText(payload.messages[payload.messages.length-1]);
    debug(shouldLog, 'PROMPT', prompt);
    if (!payload.metadata.language) {
        payload.metadata.language = await detectLanguage(prompt) || AGENT.default_language || process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE;
    }

    // Figure out the translation if configured
    const realTimeTranslation = shouldUseRealTimeTranslation(
        payload.metadata.language,
        process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE,
        AGENT.auto_translate,
        AGENT.auto_translate_languages
    );
    const translatedPrompt = realTimeTranslation ? await translateToLanguage(prompt, payload.metadata.language, process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE) : prompt;
    debug(shouldLog, 'TRANSLATED PROMPT', translatedPrompt);

    // Set the conversation ID if not API
    if (isFrontend && !payload.metadata.conversation) {
        payload.metadata.conversation = await getSessionValue('conversation_id');
    }
    debug(shouldLog, 'CONVERSATION', payload.metadata.conversation ?? null);

    // Get the config ID
    const configId = await getConfigId(AGENT, MODEL, payload);

    // Log the new prompt and get the prompt ID
    const integrationId = integration ? integration.id : null;
    const promptId = await getPromptId(
        prompt,
        payload,
        AGENT,
        configId,
        realTimeTranslation,
        translatedPrompt,
        client,
        integrationId,
        tokenId
    );
    if (!promptId) {
        debug(true, 'PROMPT INSERT FAILED');
    }

    // Get the RAG snippets
    const ctx = envBoolean(process.env.NEXT_PUBLIC_RAG) ? await getContext(translatedPrompt, promptId, AGENT, payload.metadata.language) : EMPTY_RAG_CONTEXT;
    debug(shouldLog, 'CONTEXT', ctx);

    // Figure out if the passed messages array has a system prompt
    let messagesHaveSystemPrompt = false;
    if (payload.messages) {

        messagesHaveSystemPrompt = payload.messages.some(item => SYSTEM_ROLES.includes(item.role));

        // Filter out any system prompts if the agent has locked the system prompt
        if (messagesHaveSystemPrompt && AGENT.lock_system_prompt) {
            payload.messages = payload.messages.filter(item => !SYSTEM_ROLES.includes(item.role));
        }

    }

    // Generate the system prompt by replacing placeholder tokens
    const responseLanguage = realTimeTranslation ? process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE : payload.metadata.language;
    const CACHE_BUSTER = getPromptCacheBuster(AGENT, configId);
    const systemPrompt = (messagesHaveSystemPrompt && !AGENT.lock_system_prompt) ?
        null :
        await getSystemPrompt(AGENT, ctx.text, responseLanguage, payload.metadata.translation, CACHE_BUSTER)
    ;

    // Generate the messages array
    let messages = [];
    if (payload.messages && (payload.messages.length >= 1)) {
        messages = payload.messages;
    } else {
        const numMemories = payload.metadata.anonymous ? 0 : (payload.metadata.max_memories ?? AGENT.max_memories ?? process.env.COMPLETION_API_MEMORIES);
        messages = await getMessages(
            systemPrompt,
            payload.metadata.session,
            payload.metadata.conversation,
            promptId,
            translatedPrompt,
            payload.metadata.shared_prompt ?? null,
            numMemories,
            realTimeTranslation
        );
    }
    debug(shouldLog, 'MESSAGES', messages);
    debug(shouldLog, 'PAYLOAD.MESSAGES', payload.messages);
    debug(shouldLog, 'PAYLOAD.MESSAGES.LENGTH', payload.messages?.length);

    // Set the chat completion request body
    const completionQry = getCompletionQuery(
        payload,
        AGENT,
        PROVIDER,
        MODEL,
        systemPrompt,
        messages,
        PROVIDER.custom ?? {}
    );

    const responseParams = {
        id: promptId.toString(),
        sources: ctx.sources,
        object: 'chat.completion',
        created: Math.floor(new Date().getTime() / 1000),
        model: MODEL.key,
        stream: payload.stream,
        metadata: payload.metadata,
        user: payload.user,
        usage: {},
        timings: {},
        system_fingerprint: configId.toString(),
    };

    // If the Agent has any active CTAs, send down the counts & cta
    const ctas = await getCtas(AGENT.id, client, integrationId);
    debug(shouldLog, 'CTAs', ctas);
    if (ctas.length > 0) {
        responseParams.response_counts = await getResponseCounts(
            AGENT.id,
            payload.metadata?.conversation ?? null,
            payload.metadata?.session ?? null,
            payload.metadata?.device ?? null,
            payload.user ?? null
        );
        debug(shouldLog, 'RESPONSE COUNTS', responseParams.response_counts);
        responseParams.cta = getMessageCta(ctas, responseParams.response_counts, payload.metadata.language, !isFrontend);
    }

    // Set the response options
    const responseOptions = {
        status: 200,
        headers: {
            'Content-Type': `${CONTENT_TYPES[payload.response_format.type]}; charset=utf-8`,
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
        },
    };

    // Log the prompt response start time
    waitUntil(setCompletionStart(promptId));

    // Return a streaming response if streaming is enabled
    debug(shouldLog, 'STREAM', (payload.stream ? 'YES' : 'NO'));
    if (payload.stream) {
        return getStreamingResponse(
            isFrontend,
            completionQry,
            payload,
            promptId,
            prompt,
            messages,
            realTimeTranslation,
            responseParams,
            responseOptions,
            MODEL.fallback_model_id,
            cacheTtl,
            AGENT.id,
            configId,
            startTime
        );
    } else {
        return await getNonStreamingResponse(
            completionQry,
            payload,
            promptId,
            prompt,
            messages,
            realTimeTranslation,
            responseParams,
            responseOptions,
            MODEL.fallback_model_id,
            cacheTtl,
            AGENT.id,
            configId,
            startTime
        );
    }

}

async function getStreamingResponse(
    isFrontend,
    completionQry,
    payload,
    promptId,
    prompt,
    messages,
    realTimeTranslation,
    responseParams,
    responseOptions,
    fallbackModelId,
    cacheTtl,
    agentId,
    configId,
    startTime
) {

    let tmpText = '';
    let fullText = '';
    let fullTextTranslated = '';
    let reasoning = '';
    let thinking = false;
    const transformStream = () => options => new TransformStream({

        async transform(chunk, controller) {

            // debug(shouldLog, chunk);
            if (chunk.type === 'text-delta') {

                // TODO: OpenAI reasoning models don't seem to pass back reasoning summaries ...
                if (!thinking && (chunk.text.indexOf(REASONING_START_SEQUENCE) !== -1)) {
                    thinking = true;
                }

                if (thinking) {

                    let endThinkingPos = chunk.text.indexOf(REASONING_END_SEQUENCE);
                    let beforeEndThink = null;
                    let afterEndThink = null;

                    if (endThinkingPos !== -1) {
                        beforeEndThink = chunk.text.slice(0, endThinkingPos);
                        afterEndThink = chunk.text.slice(endThinkingPos + REASONING_END_SEQUENCE.length);
                        reasoning += beforeEndThink;
                        chunk.text = afterEndThink;
                        thinking = false;
                    } else {
                        reasoning += chunk.text;
                    }

                }

                if (!thinking) {

                    fullText += chunk.text;
                    if (realTimeTranslation) {

                        tmpText += chunk.text;
                        let lines = tmpText.split(/\n/).map(line => line.trim());
                        tmpText = lines.pop();

                        for (let line of lines) {
                            if (line.length === 0) continue;
                            try {
                                let translatedChunk = await translateToLanguage(
                                    line,
                                    process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE,
                                    payload.metadata.language
                                );
                                fullTextTranslated += translatedChunk;
                                controller.enqueue({
                                    ...chunk,
                                    text: transformCompletion(translatedChunk, (payload.response_format.type === 'html'))
                                });
                            } catch (e) {
                                debug(shouldLog, 'ERROR', e);
                            }
                        }

                    } else {
                        controller.enqueue(chunk);
                    }

                }

            } else {
                controller.enqueue(chunk);
            }

        },

    });

    const streamingCompletionSettings = {
        ...completionQry,
        ...{
            onFinish: (result) => {
                debug(shouldLog, 'STREAM FINISH', result);
                setCompletionCache(completionQry, result.text, cacheTtl, agentId, configId);
                waitUntil(setCompletionFinish(
                    promptId,
                    prompt,
                    messages,
                    realTimeTranslation ? fullTextTranslated : fullText,
                    reasoning,
                    realTimeTranslation,
                    payload.metadata.language,
                    result.totalUsage,
                    realTimeTranslation ? fullText : null
                ));
            },
            onError: (error) => {
                debug(true, 'ERROR', error);
                return {
                    errorCode: 'STREAM_ERROR',
                    message: 'An error occurred while processing your request.',
                };
            },
            onAbort: async (result) => {
                debug(true, 'STREAM_ABORTED', result);
                return {
                    errorCode: 'STREAM_ABORTED',
                    message: 'Stream aborted.',
                };
            },
            experimental_transform: [transformStream(), smoothStream()],
        }
    };

    const completion = await getStreamingCompletion(streamingCompletionSettings, fallbackModelId, cacheTtl, agentId, configId);
    if (!completion) {
        return get408Response();
    }
    // debug(shouldLog, 'RESPONSE', JSON.stringify(completion, 2));

    if (payload.response_format.type === 'raw') {
        return getRawStreamingResponse(completion, responseParams, responseOptions);

    } else {

        const completionData = decorateCompletionParams(responseParams, messages, null, null, startTime);

        if (isFrontend) {
            let streaming = false;
            return completion.toUIMessageStreamResponse({

                ...responseOptions,

                messageMetadata: ({ part }) => {

                    // Send the metadata we know up front
                    if (part.type === 'start') {
                        return {
                            ...completionData,
                            metadata: {
                                ...(completionData.metadata ?? {}),
                                conversation: payload.metadata?.conversation ?? null,
                            },
                            prompt: prompt,
                        };
                    }

                    // Send a realtime update re: whether text is actually streaming (vs. just reasoning)
                    if (part.type === 'text-delta' && !streaming) {
                        if (part.text.trim().length > 0) {
                            streaming = true;
                            return { streaming: streaming };
                        }
                    }

                    // Tack on the usage info at the end
                    if (part.type === 'finish') {
                        return {
                            usage: part.totalUsage,
                            streaming: streaming,
                        };
                    }

                },

                onFinish: (result) => {
                    debug(shouldLog, 'MESSAGE FINISH', result.responseMessage);
                    const completion = getMessageText(result.responseMessage);
                    const { response, reasoning } = parseCompletion(completion);
                    waitUntil(setCompletionFinish(
                        promptId,
                        prompt,
                        messages,
                        realTimeTranslation ? fullTextTranslated : fullText,
                        reasoning,
                        realTimeTranslation,
                        payload.metadata.language,
                        result.responseMessage.metadata.usage,
                        realTimeTranslation ? fullText : null
                    ));
                },

            });

        } else {
            responseOptions.headers[COMPLETION_HEADER] = encodeURIComponent(JSON.stringify(completionData));
            return completion.toTextStreamResponse(responseOptions);
        }

    }

}

async function getStreamingCompletion(completionQry, fallbackModelId, cacheTtl, agentId, configId) {
    try {
        return await getCachedCompletion(completionQry, true, cacheTtl, agentId, configId);
    } catch(e) {
        debug(shouldLog, 'ERROR', e);
        if (fallbackModelId) {
            return await getCachedCompletion(await getFallbackCompletionQuery(completionQry, fallbackModelId), true, cacheTtl, agentId, configId);
        } else {
            return false;
        }
    }
}

function getRawStreamingResponse(completion, responseParams, responseOptions) {

    // Create OpenAI-compatible SSE stream
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
        async start(controller) {

            // Send initial chunk with role
            const initialChunk = getOpenAIResponseChunk(responseParams, { role: 'assistant', content: '' });
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(initialChunk)}\n\n`));

            try {

                // Stream the text deltas
                for await (const textPart of completion.textStream) {
                    const chunk = getOpenAIResponseChunk(responseParams,{ content: textPart });
                    controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunk)}\n\n`));
                }

                // Send final chunk with finish_reason
                const finalChunk = getOpenAIResponseChunk(responseParams, {}, 'stop');
                controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalChunk)}\n\n`));
                controller.enqueue(encoder.encode(`data: [DONE]\n\n`));

            } catch (error) {
                debug(shouldLog, 'STREAMING ERROR', error);
                controller.error(error);
            } finally {
                controller.close();
            }

        }
    });

    return new Response(stream, responseOptions);

}

async function getNonStreamingResponse(
    completionQry,
    payload,
    promptId,
    prompt,
    messages,
    realTimeTranslation,
    responseParams,
    responseOptions,
    fallbackModelId,
    cacheTtl,
    agentId,
    configId,
    startTime,
) {

    const completion = await getNonStreamingCompletion(completionQry, fallbackModelId, cacheTtl, agentId, configId);
    if (!completion) {
        return get408Response();
    }

    const { response, reasoning } = parseCompletion(completion.text);
    debug(shouldLog, 'COMPLETION', completion);

    const translatedCompletion = await translateToLanguage(
        response,
        process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE,
        payload.metadata.language
    );
    waitUntil(setCompletionFinish(
        promptId,
        prompt,
        messages,
        response,
        reasoning,
        realTimeTranslation,
        payload.metadata.language,
        completion.totalUsage
    ));

    const transformedCompletion = transformCompletion(translatedCompletion, (payload.response_format.type === 'html'));

    let formattedCompletion = transformedCompletion;
    if (payload.response_format.type === 'json') {
        formattedCompletion = responseParams;
        formattedCompletion.choices = [
            {
                index: 0,
                message: {
                    role: 'assistant',
                    content: transformedCompletion,
                },
                logprobs: null,
                finish_reason: 'stop'
            }
        ];
        formattedCompletion = JSON.stringify(decorateCompletionParams(formattedCompletion, messages, response, reasoning, startTime));
    }
    debug(shouldLog, 'NON-STREAMING RESPONSE', JSON.stringify(formattedCompletion, 2));

    return new Response(
        formattedCompletion,
        responseOptions
    );

}

async function getNonStreamingCompletion(completionQry, fallbackModelId, cacheTtl, agentId, configId) {
    try {
        return await getCachedCompletion(completionQry, false, cacheTtl, agentId, configId);
    } catch (e) {
        debug(shouldLog, 'ERROR', e);
        if (fallbackModelId) {
            return await getCachedCompletion(await getFallbackCompletionQuery(completionQry, fallbackModelId), false, cacheTtl, agentId, configId);
        } else {
            return false;
        }
    }
}

async function getFallbackCompletionQuery(completionQry, fallbackModelId) {
    debug(shouldLog, 'COMPLETION FAILED, USING FALLBACK MODEL' + fallbackModelId);
    const model = await getAgentModelById(fallbackModelId);
    const provider = getModelProvider(model);
    completionQry.model = getCompletionQueryModel(model, provider);
    return completionQry;
}

// Get the current runtime client
async function getClient(tokenId, parent_host) {
    if (tokenId) {
        return 'api';
    } else if (parent_host) {
        return 'embedded';
    } else {
        return 'standalone';
    }
}

// Get snippets from vector DB API
async function getContext(prompt, promptId, agent, language) {

    const metaFilter = await getMetaFilter(agent, language);
    debug(shouldLog, 'RAG FILTER', metaFilter);

    if (metaFilter) {
        const query = prompt
            .replace('[INST]', '')
            .replace('[/INST]', '')
            .replace('[INST/]')
            .trim()
        ;
        const corpusId = agent.has_custom_corpus ? process.env.RAG_TEAM_PREFIX + agent.id : process.env.RAG_CORPUS_KEY;
        const apiKey = (agent.has_custom_corpus && agent.corpus_api_key) ? await decryptBackend(agent.corpus_api_key) : process.env.RAG_API_KEY;
        debug(shouldLog, 'RAG CORPUS', corpusId);
        const results = await searchCorpus(
            corpusId,
            apiKey,
            agent,
            query,
            metaFilter,
            parseInt(process.env.RAG_NUM_RESULTS) * 2,
            process.env.RAG_SCORE_THRESHOLD,
            process.env.RAG_LEXICAL_INTERPOLATION
        );
        if (results) {
            return prepareContext(results, prompt, promptId, agent);
        }
    }
    return EMPTY_RAG_CONTEXT;

}

// Get the meta filter used for RAG
async function getMetaFilter(agent, language) {

    const teamId = (agent.use_team_corpus ? agent.team_id : null);
    const hasCustomSources = (
        (agent.categories?.length > 0) ||
        (agent.tags?.length > 0) ||
        (agent.collections?.length > 0) ||
        (agent.contributors?.length > 0) ||
        (agent.sources?.length > 0) ||
        agent.classification_id ||
        agent.use_team_corpus
    );

    let metaFilter = '(';

    if (!agent.disable_community_corpus) {
        metaFilter += `((doc.is_approved = true) AND (doc.weight >= ${parseInt(process.env.NEXT_PUBLIC_RAG_MIN_WEIGHT)}))`;
    }

    if (hasCustomSources) {

        if (agent.sources?.length > 0) {
            const idStr = formatDocumentIds(agent.sources);
            metaFilter += prependMetaFilterCombinator(`(doc.id IN (${idStr}))`, metaFilter, 'OR');
        }

        const filters = ['categories', 'tags', 'collections', 'contributors'];
        for (const filter of filters) {
            if (agent[filter]?.length > 0) {
                for (const id of agent[filter]) {
                    metaFilter += prependMetaFilterCombinator(`(${id} IN doc.${filter})`, metaFilter, 'OR');
                }
            }
        }

        if (agent.classification_id) {
            metaFilter += prependMetaFilterCombinator(`(doc.classification = ${agent.classification_id})`, metaFilter, 'OR');
        }

        if (agent.use_team_corpus) {
            metaFilter += prependMetaFilterCombinator(`(doc.team = ${teamId})`, metaFilter, 'OR');
        }

    }

    metaFilter += ')';
    metaFilter = metaFilter.replace('()', '');

    if (metaFilter.length > 0) {
        if (language === process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE) {
            metaFilter += ` AND (doc.lang = '${language}')`;
        }
        return metaFilter;
    } else {
        return null;
    }

}

function prepareContext(results, prompt, promptId, agent) {

    let ctx = EMPTY_RAG_CONTEXT;

    // Loop through returned results and generate prompt context + log snippets
    if (results) {

        let sources = [];
        debug(shouldLog, 'RAG RESULTS', results);
        results.forEach((result) => {
            try {
                const source = expandRagDocumentAttrs(result.document_id, result.document_metadata);
                if (shouldIncludeSource(source, agent)) {
                    ctx.text += '"' + source.title + '" by ' + source.authors.join(', ') + ':';
                    ctx.text += "\n" + decode(result.text) + "\n---\n";
                    waitUntil(insertPromptSource(promptId, source.id, result.text, result.score));
                    sources.push(source);
                    debug(shouldLog, 'INCLUDE CITATION', `${source.title} [${source.id}]`);
                } else {
                    debug(shouldLog, 'INCORRECT SOURCE', `${source.title} [${source.id}]`);
                }
            } catch (error) {
                debug(shouldLog, 'ERROR', error);
            }
        });
        debug(shouldLog, 'SOURCES', sources);

        ctx.sources = filterUniqueDocs(
            sources,
            parseInt(process.env.RAG_NUM_RESULTS)
        );

    }

    return ctx;

}

function getCompletionQuery(
    payload,
    agent,
    provider,
    model,
    systemPrompt,
    messages,
    customParams,
) {

    const maxOutputTokens = parseInt(payload.max_completion_tokens ?? agent.model_max_tokens ?? model.max_tokens ?? process.env.COMPLETION_API_DEFAULT_MAX_TOKENS);

    let providerOptions = {
        openai: {},
        xai: {},
        anthropic: {},
        groq: {},
        google: { responseModalities: ['TEXT'] },
    };

    // Add reasoning effort params
    if (model.supports_reasoning_effort) {

        const reasoningEffort = payload.reasoning_effort ?? agent.model_reasoning_effort ?? 'medium';

        // OpenAI
        providerOptions.openai.reasoningEffort = reasoningEffort;
        providerOptions.openai.reasoningSummary = 'auto';

        // XAI
        providerOptions.xai.reasoningEffort = reasoningEffort;

        // Groq
        providerOptions.groq.reasoningEffort = reasoningEffort;
        providerOptions.groq.reasoningFormat = 'parsed';

        // Anthropic
        providerOptions.anthropic.sendReasoning = true;
        providerOptions.anthropic.thinking = {
            type: 'enabled',
            budgetTokens: maxOutputTokens
        };

        // Google
        providerOptions.google.thinkingConfig = {
            thinkingBudget: maxOutputTokens,
            includeThoughts: true,
        };

    }

    // Add verbosity params
    if (model.supports_verbosity) {
        providerOptions.openai.textVerbosity = payload.verbosity ?? agent.model_verbosity ?? 'medium';
    }

    let completionTimeout = parseInt(process.env.COMPLETION_TIMEOUT);
    if (model.supports_reasoning_effort && ['medium', 'high'].includes(agent.reasoning_effort)) {
        completionTimeout = completionTimeout * 2;
    }
    let completionQry = {
        model: getCompletionQueryModel(model, provider),
        messages: messages,
        maxOutputTokens: maxOutputTokens,
        providerOptions: providerOptions,
        abortSignal: AbortSignal.timeout(completionTimeout),
    };

    // Set system prompt
    if (systemPrompt) {
        completionQry.system = systemPrompt;
    }

    // Set model params if they're supported
    for (const param in MODEL_PARAM_DEFAULTS) {
        const snakeParam = camelToSnake(param);
        if (model[`supports_${snakeParam}`]) {
            completionQry[param] = parseFloat(payload[snakeParam] ?? agent[`model_${snakeParam}`] ?? paramDefaults[param]);
        }
    }

    const stopSeq = payload.stop || model.stop_sequence || process.env.COMPLETION_STOP_SEQUENCE;
    if (stopSeq && (stopSeq.length > 0)) {
        completionQry.stop = stopSeq;
    }

    completionQry = {...completionQry, ...customParams}

    debug(shouldLog, 'COMPLETION QUERY', completionQry);

    return completionQry;

}

function getCompletionQueryModel(model, provider) {
    return provider.chat ? provider.chat(model.provider_model) : provider.chatModel(model.provider_model);
}

// Get or create the agent config
async function getConfigId(agent, model, payload) {

    // Figure out system prompt
    let systemPrompt = agent.model_system_prompt;
    if (payload.messages) {
        payload.messages.forEach(function(message) {
            if (['system', 'developer'].includes(message.role)) {
                systemPrompt = message.content;
            }
        });
    }

    // See if the agent config being used already exists
    const configParams = {
        max_tokens: payload.max_completion_tokens ?? agent.model_max_tokens ?? MODEL_PARAM_DEFAULTS.maxTokens,
        temperature: payload.temperature ?? agent.model_temperature ?? MODEL_PARAM_DEFAULTS.temperature,
        top_p: payload.top_p ?? agent.model_top_p ?? MODEL_PARAM_DEFAULTS.topP,
        frequency_penalty: payload.frequency_penalty ?? agent.model_frequency_penalty ?? MODEL_PARAM_DEFAULTS.frequencyPenalty,
        presence_penalty: payload.presence_penalty ?? agent.model_presence_penalty ?? MODEL_PARAM_DEFAULTS.presencePenalty,
        reasoning_effort: payload.reasoning_effort ?? agent.model_reasoning_effort ?? null,
        verbosity: payload.verbosity ?? agent.model_verbosity ?? null,
        model_id: model.id,
        agent_id: agent.id,
        system_prompt: systemPrompt,
        model_key: model.provider_model,
    };
    debug(shouldLog, 'CONFIG PARAMS', configParams);
    let config = await getAgentConfig(configParams);
    debug(shouldLog, 'CONFIG', config);

    // If the agent config being used doesn't exist, insert a new one
    if (!config) {
        config = await insertAgentConfig(configParams);
    }

    return config.id;

}

// Insert the new prompt log and return the prompt ID
async function getPromptId(
    prompt,
    payload,
    agent,
    configId,
    realTimeTranslation,
    translatedPrompt,
    client,
    integrationId,
    tokenId
) {
    const record = await insertPrompt({
        language: payload.metadata.language,
        translation: payload.metadata.translation,
        prompt: prompt,
        config_id: configId,
        user_id: payload.user,
        device_id: payload.metadata.device,
        session_id: payload.metadata.session,
        conversation_id: payload.metadata.conversation,
        translated_prompt: realTimeTranslation ? translatedPrompt : null,
        client: client,
        agent_integration_id: integrationId,
        agent_id: agent.id,
        agent_token_id: tokenId,
    });
    return record.id;
}

// Replace system prompt placeholder tokens and return interpolated string
async function getSystemPrompt(agent, ctx, language, translation, cacheBuster) {

    // Initialize prompt pieces
    let systemPrompt = agent.model_system_prompt?.trim();
    let contextPrompt = agent.model_context_prompt?.trim();

    // Loop up to parent prompts to prepend them
    systemPrompt = await prependParentPrompt(systemPrompt, agent);

    // Prepend prompt cache buster
    systemPrompt = prependPromptCacheBuster(systemPrompt, cacheBuster);

    // Add hard-coded system instructions
    if (agent.strip_markdown) {
        systemPrompt += STRIP_MARKDOWN_INSTRUCTIONS;
    }

    // Replace language token
    const languages = JSON.parse(process.env.NEXT_PUBLIC_LANGUAGES);
    const translations = JSON.parse(process.env.NEXT_PUBLIC_TRANSLATIONS);
    contextPrompt = replaceTokens(
        contextPrompt,
        {
            language: languages[language],
            translation: translations[translation],
            passages: ctx,
        }
    );

    // Append dynamic context prompt to system prompt
    systemPrompt += "\n\n" + contextPrompt;

    debug(shouldLog, 'SYSTEM PROMPT',systemPrompt);
    return systemPrompt;

}

async function prependParentPrompt(systemPrompt, agent) {
    if (agent.parent_id) {
        const parentAgent = await getExtensibleAgentById(agent.parent_id, agent.team_id);
        if (parentAgent) {
            return await prependParentPrompt((parentAgent.model_system_prompt ?? '') + "\n\n" + (systemPrompt ?? ''), parentAgent);
        }
    }
    return systemPrompt;
}

function getPromptCacheBuster(agent, configId) {
    return `[SYSTEM PROMPT VERSION: ${agent.team_id}-${agent.id}-${configId}]`;
}

function prependPromptCacheBuster(prompt, cacheBuster) {
    return `${cacheBuster}\n\n${prompt}`;
}

// Generate the messages array
async function getMessages(
    systemPrompt,
    sessionId,
    conversationId,
    promptId,
    prompt,
    sharedPromptId,
    numMemories,
    realTimeTranslation
) {

    // Initialize the messages array
    let messages = [];

    // Unless this is not an anonymous query, add the last 3 exchanges to messages
    debug(shouldLog, 'NUM MEMORIES', numMemories);
    if (numMemories) {
        const prompts = await getRecentPrompts(
            promptId,
            sharedPromptId,
            conversationId,
            sessionId,
            realTimeTranslation,
            numMemories
        );
        prompts.reverse().forEach((prompt) => {
            if (prompt.prompt) {
                messages.push(createMessageObject('user', prompt.prompt));
            }
            if (prompt.response) {
                messages.push(createMessageObject('assistant', prompt.response));
            }
        });
    }

    // Add the most recent prompt to messages
    messages.push(createMessageObject('user', prompt));
    debug(shouldLog, 'MESSAGES', messages);

    return messages;

}

export function createMessageObject(role, content) {
    return {
        role: role,
        content: content,
    };
}

function decorateCompletionParams(completionParams, messages, rawCompletion, reasoning, startTime) {

    const chatTokens = estimateTokens(messages);
    const responseTokens = rawCompletion ? estimateTokens(rawCompletion) : 0;
    // TODO: we're using the parsed reasoning text from models that support it, but many models just return the raw tokens ... and we're not capturing that now!
    const reasoningTokens = reasoning ? estimateTokens(reasoning) : 0;
    const endTime = performance.now();
    const startTs = new Date(performance.timeOrigin + startTime);
    const endTs = new Date(performance.timeOrigin + endTime);

    completionParams.usage = {
        prompt_tokens: chatTokens,
        completion_tokens: responseTokens,
        total_tokens: chatTokens + responseTokens,
        completion_tokens_details: {
            reasoning_tokens: reasoningTokens,
            accepted_prediction_tokens: 0,
            rejected_prediction_tokens: 0
        },
    };

    completionParams.timings = {
        start: startTs.toISOString(),
        end: endTs.toISOString(),
        elapsed: endTime - startTime,
    };

    return completionParams;

}

// Log the session in the DB and return the session ID
export const logSession = async function(sessionId, language, parentUrl, parentHost, userId) {

    // See if the session already exists
    // debug(shouldLog, 'DB: SESSION');
    const session = await getSessionById(sessionId);

    // If the session doesn't exist, insert a new one
    if (!session) {

        const headersList = await headers();
        await insertSession({
            id: sessionId,
            language: language,
            user_agent: headersList.get('user-agent'),
            host: headersList.get('host'),
            ip_country: headersList.get('x-vercel-ip-country'),
            ip_region: decodeURIComponent(headersList.get('x-vercel-ip-country-region')),
            ip_city: decodeURIComponent(headersList.get('x-vercel-ip-city')),
            ip_latitude: headersList.get('x-vercel-ip-latitude'),
            ip_longitude: headersList.get('x-vercel-ip-longitude'),
            ip_timezone: headersList.get('x-vercel-ip-timezone'),
            parent_url: parentUrl,
            parent_host: parentHost,
            user_id: userId,
        })

    }

}

// Log the time the prompt response starts
export const setCompletionStart = async function(promptId) {
    debug(shouldLog, `Prompt #${promptId} completion started`);
    return updatePrompt(
        promptId,
        {},
        'response_started_at'
    );
}

// Log the time the prompt response finishes
export async function setCompletionFinish(
    promptId,
    prompt,
    messages,
    response,
    reasoning,
    realTimeTranslation,
    lng,
    usage,
    translatedResponse = null
) {

    debug(shouldLog, 'USAGE', usage);
    debug(shouldLog, 'REASONING', reasoning);
    debug(shouldLog, 'RESPONSE', response);
    debug(shouldLog, 'TRANSLATED RESPONSE', translatedResponse);

    reasoning = (reasoning && (reasoning.length > 0)) ?
        reasoning.replace(REASONING_START_SEQUENCE, '').replace(REASONING_END_SEQUENCE, '').trim() :
        null
    ;

    response = response.trim();
    if (realTimeTranslation && !translatedResponse) {
        translatedResponse = await translateToLanguage(response, process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE, lng);
    }

    if (response && (response.length > 0)) {

        const promptTokens = estimateTokens(prompt);
        const chatTokens = usage.inputTokens ?? estimateTokens(messages);
        const reasoningTokens = usage.reasoningTokens ?? estimateTokens(reasoning);
        const responseTokens = usage.outputTokens ?? estimateTokens(reasoning + response);

        debug(shouldLog, `Prompt #${promptId} completed`);
        return updatePrompt(
            promptId,
            {
                response: response,
                translated_response: translatedResponse,
                reasoning: reasoning,
                prompt_tokens: promptTokens,
                chat_tokens: chatTokens,
                response_tokens: responseTokens,
                reasoning_tokens: reasoningTokens,
                cached: cachedResponse,
            },
            'response_completed_at',
            [
                isNull(tables.prompts.response),
            ]
        );

    }

}

function getOpenAIResponseChunk(responseParams, delta, finishReason = null) {
    return {
        id: responseParams.id,
        object: `chat.completion.chunk`,
        created: responseParams.created,
        model: responseParams.model,
        system_fingerprint: responseParams.system_fingerprint,
        choices: [{
            index: 0,
            delta: delta,
            logprobs: null,
            finish_reason: finishReason
        }]
    };
}

function parseCompletion(completion) {
    let response = completion;
    let reasoning = null;
    const reasoningEndPos = completion.indexOf(REASONING_END_SEQUENCE);
    if (reasoningEndPos !== -1) {
        response = completion.substring(reasoningEndPos + REASONING_END_SEQUENCE.length).trim();
        reasoning = completion.substring(REASONING_START_SEQUENCE.length, reasoningEndPos).trim();
    }
    return { response, reasoning };
}

function estimateTokens(strOrArray) {
    if (!strOrArray) {
        return null;
    }
    if (Array.isArray(strOrArray)) {
        let tokens = 0;
        strOrArray.forEach(message => {
            tokens += estimateTokens(getMessageText(message));
        });
        return Math.ceil(tokens);
    }
    return Math.ceil(strOrArray.length / 4);
}

async function getCachedCompletion(completionQry, stream, cacheTtl, agentId, configId) {

    let response = null;

    if (cacheTtl) {
        response = await getCachedValue(normalizeKeyObject(completionQry, configId), cacheTtl, agentId, 'completion');
        debug(shouldLog, 'CACHED RESPONSE', response);
        if (response) {
            cachedResponse = true;
            debug(shouldLog, 'CACHE RESPONSE', response);
        }
    }

    if (stream) {
        if (response) {
            // Simulate streaming for cached responses
            const cachedText = response;

            // Create a simulated stream that matches the streamText interface
            response = {
                textStream: createSimulatedTextStream(cachedText),
                toUIMessageStreamResponse: (options) => {
                    const uiStream = createUIMessageStream({
                        async execute({ writer }) {
                            // Split the response into words for streaming simulation
                            const words = cachedText.split(/(\s+)/);

                            // Write initial message with empty content
                            writer.write({
                                id: 'cached-msg',
                                role: 'assistant',
                                content: '',
                            });

                            // Stream words with small delays to simulate real streaming
                            for (const word of words) {
                                // Write text delta for each word/space
                                writer.write({
                                    type: 'text-delta',
                                    textDelta: word,
                                });

                                // Add small delay between chunks to simulate streaming
                                await new Promise(resolve => setTimeout(resolve, 20));
                            }
                        },
                    });

                    return uiStream.toUIMessageStreamResponse(options);
                },
                toTextStreamResponse: (options) => {
                    // For text streaming, create a simple readable stream
                    const encoder = new TextEncoder();
                    const stream = new ReadableStream({
                        async start(controller) {
                            try {
                                for await (const chunk of createSimulatedTextStream(cachedText)) {
                                    controller.enqueue(encoder.encode(chunk));
                                }
                            } catch (error) {
                                controller.error(error);
                            } finally {
                                controller.close();
                            }
                        }
                    });

                    return new Response(stream, options);
                }
            };
        } else {
            response = streamText(completionQry);
        }
    } else {
        if (!response) {
            response = await generateText(completionQry);
            if (response) {
                await setCompletionCache(completionQry, response.text, cacheTtl, agentId, configId);
            }
            debug(shouldLog, 'LLM RESPONSE', response);
        } else {
            // For cached non-streaming responses, wrap the text in an object
            // to match the expected format from generateText
            response = {
                text: response,
                totalUsage: {
                    inputTokens: 0,
                    outputTokens: estimateTokens(response),
                    totalTokens: estimateTokens(response)
                }
            };
        }
    }

    return response;

}

async function setCompletionCache(completionQry, response, cacheTtl, agentId, configId) {
    if (cacheTtl) {
        await setCachedValue(normalizeKeyObject(completionQry, configId), response, cacheTtl, agentId, 'completion');
    }
}

function createSimulatedTextStream(text) {
    const words = text.split(/(\s+)/);
    let index = 0;

    return {
        async *[Symbol.asyncIterator]() {
            for (const word of words) {
                // Add small delay between chunks to simulate streaming
                await new Promise(resolve => setTimeout(resolve, 20));
                yield word;
            }
        }
    };
}

function normalizeKeyObject(completionQry, configId) {
    return {
        messages: completionQry.messages,
        configId: configId,
        ...(completionQry.maxOutputTokens && { maxOutputTokens: completionQry.maxOutputTokens }),
        ...(completionQry.temperature && { temperature: completionQry.temperature }),
        ...(completionQry.topP && { topP: completionQry.topP }),
        ...(completionQry.frequencyPenalty && { frequencyPenalty: completionQry.frequencyPenalty }),
        ...(completionQry.presencePenalty && { presencePenalty: completionQry.presencePenalty }),
        ...(completionQry.reasoningEffort && { reasoningEffort: completionQry.reasoningEffort }),
        ...(completionQry.verbosity && { verbosity: completionQry.verbosity }),
    };
}
